#!/usr/bin/env node

/**
 * Create Default Admin User Script
 * Creates the default admin user with specified credentials
 */

require('dotenv').config();
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');

// Default admin credentials
const DEFAULT_ADMIN = {
  email: '<EMAIL>',
  password: 'Admin@123!',
  firstName: 'Admin',
  lastName: 'User',
  role: 'admin'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  step: (msg) => console.log(`${colors.cyan}[STEP]${colors.reset} ${msg}`)
};

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'srv1921.hstgr.io',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'u106832845_root',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'u106832845_nirvana',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

async function createConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    log.success('Database connection established');
    return connection;
  } catch (error) {
    log.error(`Database connection failed: ${error.message}`);
    throw error;
  }
}

async function createUsersTable(connection) {
  const createTableQuery = `
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      email VARCHAR(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      firstName VARCHAR(100) NOT NULL,
      lastName VARCHAR(100) NOT NULL,
      role ENUM('customer', 'admin') DEFAULT 'customer',
      isActive BOOLEAN DEFAULT true,
      emailVerified BOOLEAN DEFAULT false,
      createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_email (email),
      INDEX idx_role (role)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `;

  try {
    await connection.execute(createTableQuery);
    log.success('Users table created/verified');
  } catch (error) {
    log.error(`Failed to create users table: ${error.message}`);
    throw error;
  }
}

async function checkExistingAdmin(connection) {
  try {
    const [rows] = await connection.execute(
      'SELECT id, email FROM users WHERE email = ? OR role = ?',
      [DEFAULT_ADMIN.email, 'admin']
    );
    
    if (rows.length > 0) {
      log.warning(`Admin user already exists: ${rows[0].email}`);
      return rows[0];
    }
    
    return null;
  } catch (error) {
    log.error(`Failed to check existing admin: ${error.message}`);
    throw error;
  }
}

async function createAdminUser(connection) {
  try {
    // Hash the password
    log.step('Hashing admin password...');
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(DEFAULT_ADMIN.password, saltRounds);
    log.success('Password hashed successfully');

    // Insert admin user
    log.step('Creating admin user...');
    const insertQuery = `
      INSERT INTO users (email, password, firstName, lastName, role, isActive, emailVerified)
      VALUES (?, ?, ?, ?, ?, true, true)
    `;

    const [result] = await connection.execute(insertQuery, [
      DEFAULT_ADMIN.email,
      hashedPassword,
      DEFAULT_ADMIN.firstName,
      DEFAULT_ADMIN.lastName,
      DEFAULT_ADMIN.role
    ]);

    log.success(`Admin user created with ID: ${result.insertId}`);
    return result.insertId;
  } catch (error) {
    log.error(`Failed to create admin user: ${error.message}`);
    throw error;
  }
}

async function updateExistingAdmin(connection, adminId) {
  try {
    log.step('Updating existing admin user...');
    
    // Hash the new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(DEFAULT_ADMIN.password, saltRounds);
    
    const updateQuery = `
      UPDATE users 
      SET password = ?, firstName = ?, lastName = ?, isActive = true, emailVerified = true, updatedAt = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    await connection.execute(updateQuery, [
      hashedPassword,
      DEFAULT_ADMIN.firstName,
      DEFAULT_ADMIN.lastName,
      adminId
    ]);

    log.success('Admin user updated successfully');
  } catch (error) {
    log.error(`Failed to update admin user: ${error.message}`);
    throw error;
  }
}

async function verifyAdminUser(connection) {
  try {
    const [rows] = await connection.execute(
      'SELECT id, email, firstName, lastName, role, isActive, emailVerified FROM users WHERE email = ?',
      [DEFAULT_ADMIN.email]
    );

    if (rows.length === 0) {
      throw new Error('Admin user not found after creation');
    }

    const admin = rows[0];
    log.success('Admin user verification:');
    console.log(`  ID: ${admin.id}`);
    console.log(`  Email: ${admin.email}`);
    console.log(`  Name: ${admin.firstName} ${admin.lastName}`);
    console.log(`  Role: ${admin.role}`);
    console.log(`  Active: ${admin.isActive ? 'Yes' : 'No'}`);
    console.log(`  Email Verified: ${admin.emailVerified ? 'Yes' : 'No'}`);

    return admin;
  } catch (error) {
    log.error(`Failed to verify admin user: ${error.message}`);
    throw error;
  }
}

async function main() {
  console.log(`${colors.bright}Nirvana Organics - Default Admin User Creation${colors.reset}\n`);
  
  let connection;
  
  try {
    // Validate environment
    if (!process.env.DB_PASSWORD) {
      throw new Error('DB_PASSWORD environment variable is required');
    }

    // Create database connection
    log.step('Connecting to database...');
    connection = await createConnection();

    // Create users table if it doesn't exist
    log.step('Setting up users table...');
    await createUsersTable(connection);

    // Check if admin already exists
    log.step('Checking for existing admin user...');
    const existingAdmin = await checkExistingAdmin(connection);

    if (existingAdmin) {
      // Update existing admin
      await updateExistingAdmin(connection, existingAdmin.id);
    } else {
      // Create new admin
      await createAdminUser(connection);
    }

    // Verify admin user
    log.step('Verifying admin user...');
    await verifyAdminUser(connection);

    console.log('\n' + '='.repeat(60));
    console.log(`${colors.green}✅ ADMIN USER SETUP COMPLETE!${colors.reset}`);
    console.log('='.repeat(60));
    console.log(`${colors.bright}Admin Login Credentials:${colors.reset}`);
    console.log(`  Email: ${colors.cyan}${DEFAULT_ADMIN.email}${colors.reset}`);
    console.log(`  Password: ${colors.cyan}${DEFAULT_ADMIN.password}${colors.reset}`);
    console.log('\n' + colors.yellow + '⚠️  Please change the password after first login!' + colors.reset);
    console.log(`${colors.blue}Admin Panel URL: https://your-domain.com/admin${colors.reset}`);

  } catch (error) {
    log.error(`Admin user creation failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      log.info('Database connection closed');
    }
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createAdminUser: main };
