#!/usr/bin/env node

/**
 * Temporarily Disable SSL Script
 * Modifies the .env file to disable SSL for initial deployment testing
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`),
  highlight: (msg) => console.log(`${colors.bright}🌟 ${msg}${colors.reset}`)
};

async function disableSSL() {
  try {
    log.highlight('🔒 Temporarily Disabling SSL');
    log.info('===============================');

    const envPath = path.join(process.cwd(), '.env');
    
    // Check if .env file exists
    if (!fs.existsSync(envPath)) {
      log.error('.env file not found!');
      process.exit(1);
    }

    // Read current .env content
    log.step('Reading current .env file...');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Backup original .env
    const backupPath = path.join(process.cwd(), '.env.backup');
    fs.writeFileSync(backupPath, envContent, 'utf8');
    log.success(`Backup created: ${backupPath}`);

    // SSL settings to add/modify
    const sslSettings = {
      'SSL_ENABLED': 'false',
      'SSL_REDIRECT': 'false',
      'HTTPS_REQUIRED': 'false',
      'FORCE_HTTPS': 'false',
      'SSL_CERT_PATH': '',
      'SSL_KEY_PATH': '',
      'PORT': '5000'
    };

    log.step('Modifying SSL settings...');
    
    // Process each SSL setting
    Object.entries(sslSettings).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      
      if (regex.test(envContent)) {
        // Update existing setting
        envContent = envContent.replace(regex, `${key}=${value}`);
        log.info(`Updated: ${key}=${value}`);
      } else {
        // Add new setting
        envContent += `\n${key}=${value}`;
        log.info(`Added: ${key}=${value}`);
      }
    });

    // Add comment about temporary SSL disable
    const comment = '\n# Temporary SSL disable for initial deployment testing\n# SSL certificates will be configured later\n';
    if (!envContent.includes('Temporary SSL disable')) {
      envContent = comment + envContent;
    }

    // Write modified .env file
    fs.writeFileSync(envPath, envContent, 'utf8');
    log.success('.env file updated successfully');

    // Verify changes
    log.step('Verifying changes...');
    const updatedContent = fs.readFileSync(envPath, 'utf8');
    
    let allSettingsCorrect = true;
    Object.entries(sslSettings).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=${value}$`, 'm');
      if (!regex.test(updatedContent)) {
        log.error(`Setting not found or incorrect: ${key}=${value}`);
        allSettingsCorrect = false;
      }
    });

    if (allSettingsCorrect) {
      log.success('All SSL settings have been disabled successfully');
      log.info('The application will now run on HTTP instead of HTTPS');
      log.warning('Remember to re-enable SSL after obtaining proper certificates');
    } else {
      log.error('Some SSL settings may not have been applied correctly');
    }

    log.highlight('🎉 SSL Temporarily Disabled');
    log.info('You can now start the application without SSL certificate errors');
    log.info('To restore SSL settings, use: cp .env.backup .env');

  } catch (error) {
    log.error('Failed to disable SSL:');
    log.error(error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  disableSSL();
}

module.exports = { disableSSL };
