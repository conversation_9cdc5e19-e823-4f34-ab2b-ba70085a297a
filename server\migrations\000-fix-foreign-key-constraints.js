/**
 * Fix Foreign Key Constraints Migration
 * This migration fixes the errno: 150 foreign key constraint issues
 * by creating tables in the correct order and handling constraints properly
 */

const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('🔄 Running foreign key constraint fix migration...');

      // First, check if tables already exist and drop them if they do
      const tables = await queryInterface.showAllTables();
      console.log('📋 Existing tables:', tables);

      // Drop tables in reverse dependency order if they exist
      const tablesToDrop = ['Orders', 'Products', 'Users', 'Categories', 'roles'];
      for (const table of tablesToDrop) {
        if (tables.includes(table)) {
          console.log(`🗑️ Dropping existing table: ${table}`);
          await queryInterface.dropTable(table);
        }
      }

      // Step 1: Create roles table first (no dependencies)
      console.log('📝 Creating roles table...');
      await queryInterface.createTable('roles', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING(50),
          allowNull: false,
          unique: true
        },
        displayName: {
          type: DataTypes.STRING(100),
          allowNull: false,
          field: 'display_name'
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        permissions: {
          type: DataTypes.JSON,
          allowNull: false,
          defaultValue: '{}'
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          field: 'is_active'
        },
        priority: {
          type: DataTypes.INTEGER,
          defaultValue: 0
        },
        isSystemRole: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          field: 'is_system_role'
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      // Step 2: Create Categories table (no dependencies)
      console.log('📝 Creating Categories table...');
      await queryInterface.createTable('Categories', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: DataTypes.STRING,
          allowNull: false
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true
        },
        slug: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true
        },
        parentId: {
          type: DataTypes.INTEGER,
          allowNull: true,
          field: 'parent_id'
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          field: 'is_active'
        },
        sortOrder: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          field: 'sort_order'
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      // Step 3: Create Users table (depends on roles)
      console.log('📝 Creating Users table...');
      await queryInterface.createTable('Users', {
        id: {
          type: DataTypes.INTEGER,
          primaryKey: true,
          autoIncrement: true
        },
        firstName: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'first_name'
        },
        lastName: {
          type: DataTypes.STRING,
          allowNull: false,
          field: 'last_name'
        },
        email: {
          type: DataTypes.STRING,
          allowNull: false,
          unique: true
        },
        password: {
          type: DataTypes.STRING,
          allowNull: true // Allow null for social authentication users
        },
        phone: {
          type: DataTypes.STRING(20),
          allowNull: true
        },
        dateOfBirth: {
          type: DataTypes.DATEONLY,
          allowNull: true,
          field: 'date_of_birth'
        },
        roleId: {
          type: DataTypes.INTEGER,
          allowNull: true,
          field: 'role_id'
          // Foreign key constraint will be added later
        },
        isEmailVerified: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          field: 'is_email_verified'
        },
        emailVerificationToken: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'email_verification_token'
        },
        emailVerificationExpires: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'email_verification_expires'
        },
        passwordResetToken: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'password_reset_token'
        },
        passwordResetExpires: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'password_reset_expires'
        },
        isActive: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          field: 'is_active'
        },
        lastLoginAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'last_login_at'
        },
        googleId: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'google_id'
        },
        facebookId: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'facebook_id'
        },
        squareId: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'square_id'
        },
        profilePicture: {
          type: DataTypes.STRING,
          allowNull: true,
          field: 'profile_picture'
        },
        membershipType: {
          type: DataTypes.ENUM('first-time', 'regular', 'premium'),
          defaultValue: 'first-time',
          field: 'membership_type'
        },
        trafficSource: {
          type: DataTypes.ENUM('organic', 'direct', 'social-media', 'referral', 'paid'),
          allowNull: true,
          field: 'traffic_source'
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });

      console.log('✅ All tables created successfully');

      // Step 4: Add foreign key constraints after all tables exist
      console.log('🔗 Adding foreign key constraints...');

      // Add foreign key constraint for Users.roleId -> roles.id
      await queryInterface.addConstraint('Users', {
        fields: ['role_id'],
        type: 'foreign key',
        name: 'fk_users_role_id',
        references: {
          table: 'roles',
          field: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
      });

      // Add foreign key constraint for Categories.parentId -> Categories.id
      await queryInterface.addConstraint('Categories', {
        fields: ['parent_id'],
        type: 'foreign key',
        name: 'fk_categories_parent_id',
        references: {
          table: 'Categories',
          field: 'id'
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
      });

      console.log('✅ Foreign key constraints added successfully');

      // Step 5: Insert default roles
      console.log('📝 Inserting default roles...');
      await queryInterface.bulkInsert('roles', [
        {
          name: 'admin',
          display_name: 'Administrator',
          description: 'System administrator with full access',
          permissions: JSON.stringify(['*']),
          priority: 100,
          is_system_role: true,
          is_active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: 'manager',
          display_name: 'Manager',
          description: 'Store manager with limited admin access',
          permissions: JSON.stringify(['products:*', 'orders:*', 'customers:read']),
          priority: 50,
          is_system_role: true,
          is_active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: 'customer',
          display_name: 'Customer',
          description: 'Regular customer with basic access',
          permissions: JSON.stringify(['profile:*', 'orders:read']),
          priority: 10,
          is_system_role: true,
          is_active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);

      console.log('✅ Foreign key constraint fix migration completed successfully');
    } catch (error) {
      console.error('❌ Error running foreign key constraint fix migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      console.log('🔄 Reverting foreign key constraint fix migration...');

      // Drop foreign key constraints first
      await queryInterface.removeConstraint('Users', 'fk_users_role_id');
      await queryInterface.removeConstraint('Categories', 'fk_categories_parent_id');

      // Drop tables in reverse order
      await queryInterface.dropTable('Users');
      await queryInterface.dropTable('Categories');
      await queryInterface.dropTable('roles');

      console.log('✅ Foreign key constraint fix migration reverted successfully');
    } catch (error) {
      console.error('❌ Error reverting foreign key constraint fix migration:', error);
      throw error;
    }
  }
};
