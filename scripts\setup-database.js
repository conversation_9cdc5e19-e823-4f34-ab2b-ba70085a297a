#!/usr/bin/env node

/**
 * Database Setup Script for Nirvana Organics E-commerce
 * This script creates and initializes the MySQL database with all required tables
 */

const { Sequelize } = require('sequelize');
const path = require('path');
const dotenv = require('dotenv');

dotenv.config({ path: path.join(__dirname, '../.env') });

// Note: Models will be loaded dynamically when needed

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'nirvana_organics',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql',
  logging: console.log,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`)
};

/**
 * Test database connection
 */
async function testConnection(sequelize) {
  try {
    await sequelize.authenticate();
    log.success('Database connection established successfully');
    return true;
  } catch (error) {
    log.error(`Unable to connect to database: ${error.message}`);
    return false;
  }
}

/**
 * Create database tables using raw SQL
 */
async function createTables(sequelize) {
  try {
    log.step('Creating database tables...');

    // Create tables using raw SQL to avoid model dependencies
    const tableQueries = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        firstName VARCHAR(50) NOT NULL,
        lastName VARCHAR(50) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('customer', 'admin', 'super_admin') DEFAULT 'customer',
        isVerified BOOLEAN DEFAULT false,
        isActive BOOLEAN DEFAULT true,
        phoneNumber VARCHAR(20),
        dateOfBirth DATE,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      // Categories table
      `CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        isActive BOOLEAN DEFAULT true,
        sortOrder INT DEFAULT 0,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      // Products table with explicit constraint name to avoid errno 121
      `CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        shortDescription VARCHAR(500),
        price DECIMAL(10,2) NOT NULL,
        comparePrice DECIMAL(10,2),
        sku VARCHAR(100) UNIQUE,
        categoryId INT,
        isActive BOOLEAN DEFAULT true,
        isFeatured BOOLEAN DEFAULT false,
        stockQuantity INT DEFAULT 0,
        images JSON,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT fk_products_category_nirvana
          FOREIGN KEY (categoryId) REFERENCES categories(id)
          ON DELETE SET NULL ON UPDATE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
    ];

    for (let i = 0; i < tableQueries.length; i++) {
      const query = tableQueries[i];
      const tableName = ['users', 'categories', 'products'][i];

      try {
        await sequelize.query(query);
        log.success(`Table '${tableName}' created successfully`);
      } catch (tableError) {
        // Handle specific errno 121 (duplicate key/constraint)
        if (tableError.message.includes('errno: 121') || tableError.message.includes('Duplicate key')) {
          log.warning(`Table '${tableName}' has constraint conflicts. Attempting to fix...`);

          if (tableName === 'products') {
            // Drop and recreate products table to fix constraint conflicts
            try {
              await sequelize.query('DROP TABLE IF EXISTS products');
              await sequelize.query(query);
              log.success(`Table '${tableName}' recreated successfully after fixing constraints`);
            } catch (recreateError) {
              log.error(`Failed to recreate '${tableName}' table: ${recreateError.message}`);
              throw recreateError;
            }
          } else {
            throw tableError;
          }
        } else {
          throw tableError;
        }
      }
    }

    log.success('Essential database tables created successfully');
    log.info('Note: Additional tables will be created when the application starts');
    return true;
  } catch (error) {
    log.error(`Failed to create tables: ${error.message}`);
    if (error.message.includes('errno: 121')) {
      log.error('This is a foreign key constraint conflict (errno 121)');
      log.error('Run the fix script: ./deployment/scripts/fix-mysql-errno-121.sh');
    }
    return false;
  }
}

/**
 * Seed initial data using raw SQL queries
 */
async function seedInitialData(sequelize) {
  try {
    log.step('Seeding initial data...');

    // Create admin user if not exists (using raw SQL)
    const [adminResults] = await sequelize.query(`
      INSERT IGNORE INTO users (
        firstName, lastName, email, password, role, isVerified, isActive, createdAt, updatedAt
      ) VALUES (
        'Admin', 'User', '<EMAIL>',
        '$2b$12$LQv3c1yqBwlVHpPjrGNDve/.oz9NtjqTvDXDchLdU5JlKGOHxa5Oy',
        'super_admin', true, true, NOW(), NOW()
      )
    `);

    if (adminResults.affectedRows > 0) {
      log.success('Admin user created');
    } else {
      log.info('Admin user already exists');
    }

    // Create default categories using raw SQL
    const defaultCategories = [
      ['Flower', 'flower', 'Premium cannabis flower products', true, 1],
      ['Edibles', 'edibles', 'Cannabis-infused edible products', true, 2],
      ['Concentrates', 'concentrates', 'High-quality cannabis concentrates', true, 3],
      ['Accessories', 'accessories', 'Cannabis accessories and tools', true, 4]
    ];

    for (const [name, slug, description, isActive, sortOrder] of defaultCategories) {
      const [categoryResults] = await sequelize.query(`
        INSERT IGNORE INTO categories (
          name, slug, description, isActive, sortOrder, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, {
        replacements: [name, slug, description, isActive, sortOrder]
      });

      if (categoryResults.affectedRows > 0) {
        log.success(`Category '${name}' created`);
      } else {
        log.info(`Category '${name}' already exists`);
      }
    }

    log.success('Initial data seeded successfully');
    return true;
  } catch (error) {
    log.error(`Failed to seed initial data: ${error.message}`);
    log.warning('This is normal if tables don\'t exist yet - run table creation first');
    return false;
  }
}

/**
 * Create database indexes for performance
 */
async function createIndexes(sequelize) {
  try {
    log.step('Creating database indexes...');
    
    const queries = [
      // Product indexes (using correct column names)
      'CREATE INDEX IF NOT EXISTS idx_products_categoryId ON products(categoryId)',
      'CREATE INDEX IF NOT EXISTS idx_products_isActive ON products(isActive)',
      'CREATE INDEX IF NOT EXISTS idx_products_isFeatured ON products(isFeatured)',
      'CREATE INDEX IF NOT EXISTS idx_products_price ON products(price)',
      'CREATE INDEX IF NOT EXISTS idx_products_createdAt ON products(createdAt)',

      // User indexes (using correct column names)
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_isActive ON users(isActive)',
      'CREATE INDEX IF NOT EXISTS idx_users_isVerified ON users(isVerified)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',

      // Category indexes
      'CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug)',
      'CREATE INDEX IF NOT EXISTS idx_categories_isActive ON categories(isActive)',
      'CREATE INDEX IF NOT EXISTS idx_categories_sortOrder ON categories(sortOrder)'
    ];
    
    for (const query of queries) {
      await sequelize.query(query);
    }
    
    log.success('Database indexes created successfully');
    return true;
  } catch (error) {
    log.error(`Failed to create indexes: ${error.message}`);
    return false;
  }
}

/**
 * Main setup function
 */
async function setupDatabase() {
  log.info('Starting Nirvana Organics Database Setup...');
  log.info('=====================================');
  
  // Validate environment variables
  if (!process.env.DB_HOST || !process.env.DB_NAME || !process.env.DB_USER) {
    log.error('Missing required database environment variables');
    log.error('Please ensure DB_HOST, DB_NAME, and DB_USER are set in your .env file');
    process.exit(1);
  }
  
  log.info(`Database Host: ${dbConfig.host}`);
  log.info(`Database Name: ${dbConfig.database}`);
  log.info(`Database User: ${dbConfig.username}`);
  log.info('=====================================');
  
  // Create Sequelize instance
  const sequelize = new Sequelize(
    dbConfig.database,
    dbConfig.username,
    dbConfig.password,
    {
      host: dbConfig.host,
      port: dbConfig.port,
      dialect: dbConfig.dialect,
      logging: dbConfig.logging,
      pool: dbConfig.pool,
      define: dbConfig.define
    }
  );
  
  try {
    // Step 1: Test connection
    const connectionSuccess = await testConnection(sequelize);
    if (!connectionSuccess) {
      process.exit(1);
    }
    
    // Step 2: Create tables
    const tablesSuccess = await createTables(sequelize);
    if (!tablesSuccess) {
      process.exit(1);
    }
    
    // Step 3: Create indexes
    const indexesSuccess = await createIndexes(sequelize);
    if (!indexesSuccess) {
      log.warning('Some indexes may not have been created, but continuing...');
    }
    
    // Step 4: Seed initial data
    const seedSuccess = await seedInitialData(sequelize);
    if (!seedSuccess) {
      log.warning('Initial data seeding failed, but database structure is ready');
    }
    
    log.success('Database setup completed successfully!');
    log.info('=====================================');
    log.info('Next steps:');
    log.info('1. Update your .env file with the correct database credentials');
    log.info('2. Test your application connection');
    log.info('3. Configure SSL certificates');
    log.info('=====================================');
    
  } catch (error) {
    log.error(`Database setup failed: ${error.message}`);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run setup if called directly (ES module equivalent)
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDatabase().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

export { setupDatabase };
