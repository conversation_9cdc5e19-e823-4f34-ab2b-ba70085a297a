#!/usr/bin/env node

/**
 * Fix All bcrypt Imports Script
 * Finds and fixes all remaining bcrypt imports in the codebase
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`),
  highlight: (msg) => console.log(`${colors.bright}🌟 ${msg}${colors.reset}`)
};

// Function to recursively find all JavaScript files
function findJSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(file)) {
        findJSFiles(filePath, fileList);
      }
    } else if (file.endsWith('.js') && !file.includes('.min.')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Function to check if file contains bcrypt import
function checkBcryptImport(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const bcryptRegex = /require\(['"]bcrypt['"]\)/g;
    const matches = content.match(bcryptRegex);
    return matches ? matches.length : 0;
  } catch (error) {
    log.error(`Error reading file ${filePath}: ${error.message}`);
    return 0;
  }
}

// Function to fix bcrypt import in a file
function fixBcryptImport(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Replace require('bcrypt') with require('bcryptjs')
    content = content.replace(/require\(['"]bcrypt['"]\)/g, "require('bcryptjs')");
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    return false;
  } catch (error) {
    log.error(`Error fixing file ${filePath}: ${error.message}`);
    return false;
  }
}

async function fixAllBcryptImports() {
  try {
    log.highlight('🚀 Starting bcrypt Import Fix');
    log.info('=====================================');

    // Find all JavaScript files
    log.step('Scanning for JavaScript files...');
    const jsFiles = findJSFiles(process.cwd());
    log.info(`Found ${jsFiles.length} JavaScript files`);

    // Check for bcrypt imports
    log.step('Checking for bcrypt imports...');
    const filesWithBcrypt = [];
    
    jsFiles.forEach(filePath => {
      const bcryptCount = checkBcryptImport(filePath);
      if (bcryptCount > 0) {
        filesWithBcrypt.push({
          path: filePath,
          count: bcryptCount
        });
      }
    });

    if (filesWithBcrypt.length === 0) {
      log.success('No files with bcrypt imports found!');
      return;
    }

    log.warning(`Found ${filesWithBcrypt.length} files with bcrypt imports:`);
    filesWithBcrypt.forEach(file => {
      log.info(`  ${file.path} (${file.count} imports)`);
    });

    // Fix bcrypt imports
    log.step('Fixing bcrypt imports...');
    let fixedCount = 0;
    
    filesWithBcrypt.forEach(file => {
      const fixed = fixBcryptImport(file.path);
      if (fixed) {
        log.success(`Fixed: ${file.path}`);
        fixedCount++;
      } else {
        log.warning(`No changes needed: ${file.path}`);
      }
    });

    log.highlight(`🎉 Fixed bcrypt imports in ${fixedCount} files`);

    // Verify fixes
    log.step('Verifying fixes...');
    const remainingFiles = [];
    
    filesWithBcrypt.forEach(file => {
      const bcryptCount = checkBcryptImport(file.path);
      if (bcryptCount > 0) {
        remainingFiles.push(file.path);
      }
    });

    if (remainingFiles.length === 0) {
      log.success('All bcrypt imports have been fixed!');
    } else {
      log.error(`${remainingFiles.length} files still have bcrypt imports:`);
      remainingFiles.forEach(filePath => {
        log.error(`  ${filePath}`);
      });
    }

  } catch (error) {
    log.error('Failed to fix bcrypt imports:');
    log.error(error.message);
    process.exit(1);
  }
}

// Run the fix
if (require.main === module) {
  fixAllBcryptImports();
}

module.exports = { fixAllBcryptImports };
