#!/usr/bin/env node

/**
 * Fix Foreign Key Migration Script
 * Runs the specific migration to fix errno: 150 foreign key constraint issues
 */

require('dotenv').config();
const { Sequelize } = require('sequelize');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}🔄 ${msg}${colors.reset}`),
  highlight: (msg) => console.log(`${colors.bright}🌟 ${msg}${colors.reset}`)
};

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME || 'u106832845_nirvana',
  process.env.DB_USER || 'u106832845_nirvana',
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST || 'srv1921.hstgr.io',
    dialect: 'mysql',
    port: process.env.DB_PORT || 3306,
    logging: (msg) => console.log(`${colors.cyan}[SQL] ${msg}${colors.reset}`),
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    }
  }
);

async function runFixMigration() {
  try {
    log.highlight('🚀 Starting Foreign Key Constraint Fix Migration');
    log.info('================================================');

    // Test database connection
    log.step('Testing database connection...');
    await sequelize.authenticate();
    log.success('Database connection established successfully');

    // Load and run the fix migration
    log.step('Loading foreign key fix migration...');
    const migrationPath = path.join(__dirname, '../server/migrations/000-fix-foreign-key-constraints.js');
    const migration = require(migrationPath);

    if (!migration.up || typeof migration.up !== 'function') {
      throw new Error('Migration file does not export a valid "up" function');
    }

    log.step('Running foreign key constraint fix migration...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    log.success('Foreign key constraint fix migration completed successfully');

    // Verify tables were created
    log.step('Verifying table creation...');
    const tables = await sequelize.getQueryInterface().showAllTables();
    log.info(`Created tables: ${tables.join(', ')}`);

    // Verify foreign key constraints
    log.step('Verifying foreign key constraints...');
    const constraints = await sequelize.query(`
      SELECT 
        CONSTRAINT_NAME,
        TABLE_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE CONSTRAINT_SCHEMA = '${process.env.DB_NAME}' 
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `, { type: Sequelize.QueryTypes.SELECT });

    if (constraints.length > 0) {
      log.success('Foreign key constraints verified:');
      constraints.forEach(constraint => {
        log.info(`  ${constraint.TABLE_NAME}.${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
      });
    } else {
      log.warning('No foreign key constraints found - this may indicate an issue');
    }

    // Verify roles were inserted
    log.step('Verifying default roles...');
    const roleCount = await sequelize.query('SELECT COUNT(*) as count FROM roles', { 
      type: Sequelize.QueryTypes.SELECT 
    });
    log.success(`Default roles created: ${roleCount[0].count} roles`);

    log.highlight('🎉 Foreign Key Constraint Fix Migration Completed Successfully!');
    log.info('You can now proceed with creating the admin user and starting the application.');

  } catch (error) {
    log.error('Foreign key constraint fix migration failed:');
    log.error(error.message);
    
    if (error.sql) {
      log.error(`SQL: ${error.sql}`);
    }
    
    if (error.original) {
      log.error(`Original error: ${error.original.message}`);
    }
    
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
if (require.main === module) {
  runFixMigration();
}

module.exports = { runFixMigration };
